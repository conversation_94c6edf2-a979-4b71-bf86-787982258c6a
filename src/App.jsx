import React, { useState, useEffect } from 'react';

// D<PERSON> liệu dinh dưỡng cho 100g mỗi loại thực phẩm
const foodData = {
  // Trứng và sản phẩm từ trứng
  'Trứng gà luộc': { calo: 155, protein: 13, carb: 1.1, fat: 11 },
  'Trứng chiên ít dầu': { calo: 196, protein: 13.6, carb: 0.7, fat: 15 },
  'Lòng trắng trứng': { calo: 52, protein: 11, carb: 0.7, fat: 0.2 },

  // Thịt và cá
  'Ức gà kho gừng': { calo: 165, protein: 31, carb: 0, fat: 3.6 },
  'Ức gà hấp': { calo: 165, protein: 31, carb: 0, fat: 3.6 },
  '<PERSON><PERSON> chiên nước mắm (không da)': { calo: 190, protein: 29, carb: 2, fat: 7 },
  'Thịt heo nạc luộc': { calo: 143, protein: 26, carb: 0, fat: 3.5 },
  'Thịt heo nạc bằm': { calo: 143, protein: 26, carb: 0, fat: 3.5 },
  'Thịt bò xào': { calo: 250, protein: 26, carb: 0, fat: 15 },
  'Bò kho': { calo: 200, protein: 25, carb: 3, fat: 10 },
  'Cá basa áp chảo': { calo: 120, protein: 20, carb: 0, fat: 4 },
  'Cá basa nướng giấy bạc': { calo: 90, protein: 19, carb: 0, fat: 1.5 },
  'Cá điêu hồng chiên áp chảo': { calo: 128, protein: 20, carb: 0, fat: 4.5 },
  'Cá hộp (cá nục sốt cà)': { calo: 158, protein: 25, carb: 4, fat: 5 },

  // Rau củ và trái cây
  'Khoai lang luộc': { calo: 86, protein: 1.6, carb: 20, fat: 0.1 },
  'Cơm trắng (gạo tẻ)': { calo: 130, protein: 2.7, carb: 28, fat: 0.3 },
  'Bánh mì nguyên cám': { calo: 247, protein: 13, carb: 41, fat: 4.2 },
  'Rau muống luộc': { calo: 18, protein: 2.6, carb: 3.1, fat: 0.2 },
  'Cải thìa luộc': { calo: 13, protein: 1.5, carb: 2.2, fat: 0.2 },
  'Cải ngọt xào': { calo: 20, protein: 2, carb: 3.5, fat: 0.3 },
  'Cải xanh luộc': { calo: 25, protein: 3, carb: 4, fat: 0.4 },
  'Đậu que luộc': { calo: 35, protein: 1.8, carb: 7, fat: 0.1 },
  'Bông cải luộc': { calo: 25, protein: 3, carb: 5, fat: 0.3 },
  'Dưa leo': { calo: 16, protein: 0.7, carb: 4, fat: 0.1 },
  'Dưa leo trộn chua ngọt': { calo: 25, protein: 0.7, carb: 6, fat: 0.1 },
  'Cà chua': { calo: 18, protein: 0.9, carb: 3.9, fat: 0.2 },

  // Trái cây
  'Chuối': { calo: 89, protein: 1.1, carb: 23, fat: 0.3 },
  'Cam': { calo: 47, protein: 0.9, carb: 12, fat: 0.1 },
  'Ổi': { calo: 68, protein: 2.6, carb: 14, fat: 1 },
  'Táo': { calo: 52, protein: 0.3, carb: 14, fat: 0.2 },
  'Thanh long': { calo: 60, protein: 1.2, carb: 13, fat: 0.4 },
  'Dưa hấu': { calo: 30, protein: 0.6, carb: 8, fat: 0.2 },
  'Đu đủ': { calo: 43, protein: 0.5, carb: 11, fat: 0.3 },

  // Sữa và đậu phụ
  'Sữa tươi không đường': { calo: 42, protein: 3.4, carb: 5, fat: 1 },
  'Đậu hũ non': { calo: 76, protein: 8, carb: 1.9, fat: 4.8 },
  'Đậu phụ hấp': { calo: 76, protein: 8, carb: 1.9, fat: 4.8 },

  // Canh và nước dùng
  'Canh bí đỏ nấu thịt bằm': { calo: 40, protein: 3.5, carb: 3.5, fat: 1.5 },
  'Canh bí xanh': { calo: 25, protein: 2, carb: 4, fat: 0.5 },
  'Canh cải thìa': { calo: 20, protein: 2, carb: 2.5, fat: 0.5 },
  'Canh mồng tơi nấu tôm khô': { calo: 30, protein: 4, carb: 2, fat: 1 },
  'Canh mướp nấu tôm': { calo: 35, protein: 5, carb: 2.5, fat: 1 },
  'Canh bí nấu tôm': { calo: 35, protein: 5, carb: 3, fat: 1 },
  'Canh rau dền': { calo: 25, protein: 3, carb: 2, fat: 0.5 },

  // Món xào và nấu
  'Bí đỏ xào với thịt bằm': { calo: 60, protein: 4, carb: 6, fat: 2.5 },
  'Đậu hũ non sốt cà': { calo: 90, protein: 8, carb: 5, fat: 5 },
};

const App = () => {
  const [selectedFoods, setSelectedFoods] = useState([]);
  const [entries, setEntries] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('add'); // 'add' hoặc 'summary'

  // Tải dữ liệu từ localStorage khi component mount
  useEffect(() => {
    const savedEntries = localStorage.getItem('foodEntries');
    if (savedEntries) {
      setEntries(JSON.parse(savedEntries));
    }
  }, []);

  // Lưu dữ liệu vào localStorage khi entries thay đổi
  useEffect(() => {
    localStorage.setItem('foodEntries', JSON.stringify(entries));
  }, [entries]);

  // Thêm món ăn vào danh sách chọn
  const addToSelected = (foodName) => {
    if (!selectedFoods.find(item => item.name === foodName)) {
      setSelectedFoods([...selectedFoods, {
        name: foodName,
        grams: 100,
        id: Date.now()
      }]);
    }
  };

  // Xóa món ăn khỏi danh sách chọn
  const removeFromSelected = (id) => {
    setSelectedFoods(selectedFoods.filter(item => item.id !== id));
  };

  // Cập nhật số gram cho món ăn đã chọn
  const updateGrams = (id, newGrams) => {
    setSelectedFoods(selectedFoods.map(item =>
      item.id === id ? { ...item, grams: newGrams } : item
    ));
  };

  // Thêm tất cả món đã chọn vào entries
  const addSelectedToMeal = () => {
    if (selectedFoods.length > 0) {
      const newEntries = selectedFoods.map(item => ({
        ...item,
        id: Date.now() + Math.random(),
        addedAt: new Date().toLocaleString('vi-VN')
      }));
      setEntries([...entries, ...newEntries]);
      setSelectedFoods([]);
      setSearchTerm('');
    }
  };

  // Xóa món ăn khỏi entries
  const removeFood = (id) => {
    setEntries(entries.filter(entry => entry.id !== id));
  };

  // Xóa tất cả
  const clearAll = () => {
    setEntries([]);
  };

  // Xóa danh sách chọn
  const clearSelected = () => {
    setSelectedFoods([]);
  };

  const exportData = () => {
    const exportText = `
🥗 THÔNG TIN DINH DƯỠNG THỰC ĐƠN

📋 Danh sách món ăn:
${entries.map(item => `• ${item.name}: ${item.grams}g`).join('\n')}

📊 Tổng dinh dưỡng:
• Calories: ${totals.calo.toFixed(1)} kcal
• Protein: ${totals.protein.toFixed(1)} g (${((totals.protein / totals.calo) * 100).toFixed(1)}% calories)
• Carbohydrate: ${totals.carb.toFixed(1)} g (${((totals.carb * 4 / totals.calo) * 100).toFixed(1)}% calories)
• Fat: ${totals.fat.toFixed(1)} g (${((totals.fat * 9 / totals.calo) * 100).toFixed(1)}% calories)

💡 Khuyến nghị: Protein 15-25%, Carbs 45-65%, Fat 20-35%
    `.trim();

    navigator.clipboard.writeText(exportText).then(() => {
      alert('Đã sao chép thông tin dinh dưỡng vào clipboard!');
    }).catch(() => {
      // Fallback cho trình duyệt không hỗ trợ clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = exportText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Đã sao chép thông tin dinh dưỡng vào clipboard!');
    });
  };

  // Lọc thực phẩm theo từ khóa tìm kiếm
  const filteredFoods = Object.keys(foodData).filter(food =>
    food.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totals = entries.reduce(
    (acc, item) => {
      const data = foodData[item.name];
      const factor = item.grams / 100;
      acc.calo += data.calo * factor;
      acc.protein += data.protein * factor;
      acc.carb += data.carb * factor;
      acc.fat += data.fat * factor;
      return acc;
    },
    { calo: 0, protein: 0, carb: 0, fat: 0 }
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <h1 className="text-4xl font-bold text-center bg-gradient-to-r from-indigo-600 to-cyan-600 bg-clip-text text-transparent">
            🍽️ Quản Lý Dinh Dưỡng Thực Đơn
          </h1>
          <p className="text-center text-gray-600 mt-2">Chọn nhiều món ăn và tính toán dinh dưỡng tổng thể</p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-xl p-1 shadow-lg">
            <button
              onClick={() => setActiveTab('add')}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === 'add'
                  ? 'bg-indigo-600 text-white shadow-md'
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              🛒 Chọn Món Ăn
            </button>
            <button
              onClick={() => setActiveTab('summary')}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === 'summary'
                  ? 'bg-indigo-600 text-white shadow-md'
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              📊 Tổng Kết ({entries.length})
            </button>
          </div>
        </div>

        {activeTab === 'add' && (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Phần tìm kiếm và chọn món */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                  🔍 Tìm Kiếm Món Ăn
                </h2>

                {/* Tìm kiếm */}
                <div className="mb-6">
                  <input
                    type="text"
                    className="w-full border-2 border-gray-200 rounded-xl p-4 text-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                    placeholder="🔍 Nhập tên món ăn để tìm kiếm..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                {/* Danh sách món ăn dạng grid */}
                <div className="grid md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                  {filteredFoods.map((food) => (
                    <div
                      key={food}
                      className="border-2 border-gray-200 rounded-xl p-4 hover:border-indigo-300 hover:shadow-md transition-all cursor-pointer"
                      onClick={() => addToSelected(food)}
                    >
                      <h3 className="font-semibold text-gray-800 mb-2">{food}</h3>
                      <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                        <div>🔥 {foodData[food].calo} kcal</div>
                        <div>🥩 {foodData[food].protein}g protein</div>
                        <div>🍞 {foodData[food].carb}g carbs</div>
                        <div>🧈 {foodData[food].fat}g fat</div>
                      </div>
                      <div className="mt-3">
                        <button className="w-full bg-indigo-100 hover:bg-indigo-200 text-indigo-700 py-2 px-4 rounded-lg transition-colors">
                          ➕ Thêm vào danh sách
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Phần danh sách món đã chọn */}
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-bold text-gray-800 flex items-center">
                    🛍️ Món Đã Chọn ({selectedFoods.length})
                  </h2>
                  {selectedFoods.length > 0 && (
                    <button
                      onClick={clearSelected}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      �️ Xóa tất cả
                    </button>
                  )}
                </div>

                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {selectedFoods.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">
                      Chưa có món ăn nào được chọn
                    </p>
                  ) : (
                    selectedFoods.map((item) => (
                      <div key={item.id} className="bg-gray-50 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-medium text-gray-800">{item.name}</h3>
                          <button
                            onClick={() => removeFromSelected(item.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            ❌
                          </button>
                        </div>
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            value={item.grams}
                            onChange={(e) => updateGrams(item.id, Number(e.target.value))}
                            className="w-20 border border-gray-300 rounded px-2 py-1 text-sm"
                            min="1"
                          />
                          <span className="text-sm text-gray-600">gram</span>
                        </div>
                        <div className="grid grid-cols-2 gap-2 mt-2 text-xs text-gray-600">
                          <div>🔥 {(foodData[item.name].calo * item.grams / 100).toFixed(1)} kcal</div>
                          <div>🥩 {(foodData[item.name].protein * item.grams / 100).toFixed(1)}g</div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {selectedFoods.length > 0 && (
                  <button
                    onClick={addSelectedToMeal}
                    className="w-full mt-4 bg-gradient-to-r from-indigo-600 to-cyan-600 hover:from-indigo-700 hover:to-cyan-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200"
                  >
                    ✅ Thêm Vào Thực Đơn ({selectedFoods.length} món)
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'summary' && (
          <div className="space-y-8">
            {/* Tổng quan dinh dưỡng */}
            {entries.length > 0 && (
              <div className="bg-white rounded-xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-center text-gray-800 mb-6">
                  📊 Tổng Kết Dinh Dưỡng
                </h2>
                <div className="grid md:grid-cols-4 gap-6 mb-8">
                  <div className="bg-gradient-to-br from-orange-100 to-orange-200 p-6 rounded-xl text-center">
                    <div className="text-3xl font-bold text-orange-600 mb-2">
                      {totals.calo.toFixed(1)}
                    </div>
                    <div className="text-sm text-orange-700 font-medium">Calories (kcal)</div>
                  </div>
                  <div className="bg-gradient-to-br from-red-100 to-red-200 p-6 rounded-xl text-center">
                    <div className="text-3xl font-bold text-red-600 mb-2">
                      {totals.protein.toFixed(1)}
                    </div>
                    <div className="text-sm text-red-700 font-medium">Protein (g)</div>
                  </div>
                  <div className="bg-gradient-to-br from-blue-100 to-blue-200 p-6 rounded-xl text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {totals.carb.toFixed(1)}
                    </div>
                    <div className="text-sm text-blue-700 font-medium">Carbs (g)</div>
                  </div>
                  <div className="bg-gradient-to-br from-yellow-100 to-yellow-200 p-6 rounded-xl text-center">
                    <div className="text-3xl font-bold text-yellow-600 mb-2">
                      {totals.fat.toFixed(1)}
                    </div>
                    <div className="text-sm text-yellow-700 font-medium">Fat (g)</div>
                  </div>
                </div>

                {/* Phân tích tỷ lệ dinh dưỡng */}
                <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-xl">
                  <h3 className="font-bold text-gray-800 mb-4 text-center">💡 Phân Tích Tỷ Lệ Dinh Dưỡng</h3>
                  <div className="grid md:grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-600">
                        {((totals.protein * 4 / totals.calo) * 100).toFixed(1)}%
                      </div>
                      <div className="text-gray-600">Protein</div>
                      <div className="text-xs text-gray-500">(Khuyến nghị: 15-25%)</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-600">
                        {((totals.carb * 4 / totals.calo) * 100).toFixed(1)}%
                      </div>
                      <div className="text-gray-600">Carbohydrate</div>
                      <div className="text-xs text-gray-500">(Khuyến nghị: 45-65%)</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-yellow-600">
                        {((totals.fat * 9 / totals.calo) * 100).toFixed(1)}%
                      </div>
                      <div className="text-gray-600">Fat</div>
                      <div className="text-xs text-gray-500">(Khuyến nghị: 20-35%)</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Danh sách chi tiết món ăn */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800">
                  🍽️ Chi Tiết Thực Đơn ({entries.length} món)
                </h2>
                {entries.length > 0 && (
                  <div className="flex gap-3">
                    <button
                      onClick={exportData}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      📋 Sao Chép
                    </button>
                    <button
                      onClick={clearAll}
                      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      🗑️ Xóa Tất Cả
                    </button>
                  </div>
                )}
              </div>

              <div className="space-y-3 max-h-96 overflow-y-auto">
                {entries.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🍽️</div>
                    <p className="text-gray-500 text-lg">Chưa có món ăn nào trong thực đơn</p>
                    <p className="text-gray-400 text-sm mt-2">Hãy chuyển sang tab "Chọn Món Ăn" để thêm món</p>
                  </div>
                ) : (
                  entries.map((item) => (
                    <div key={item.id} className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-800 mb-1">{item.name}</h3>
                          <div className="text-sm text-gray-600 mb-2">{item.grams}g</div>
                          {item.addedAt && (
                            <div className="text-xs text-gray-400">Thêm lúc: {item.addedAt}</div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-2">
                            <div>🔥 {(foodData[item.name].calo * item.grams / 100).toFixed(1)} kcal</div>
                            <div>🥩 {(foodData[item.name].protein * item.grams / 100).toFixed(1)}g</div>
                            <div>🍞 {(foodData[item.name].carb * item.grams / 100).toFixed(1)}g</div>
                            <div>🧈 {(foodData[item.name].fat * item.grams / 100).toFixed(1)}g</div>
                          </div>
                          <button
                            onClick={() => removeFood(item.id)}
                            className="text-red-500 hover:text-red-700 p-1"
                          >
                            ❌
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 py-6 text-center text-sm text-gray-500">
          <p>📱 Ứng dụng quản lý dinh dưỡng thực đơn cá nhân</p>
          <p className="mt-1">Dữ liệu được lưu tự động trên trình duyệt của bạn</p>
        </div>
      </div>
    </div>
  );
};

export default App;
