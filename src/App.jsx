import React, { useState, useEffect } from 'react';

// D<PERSON> liệu dinh dưỡng được phân loại theo nhóm thực phẩm
const foodCategories = {
  '🥚 Trứng & Sản phẩm từ trứng': {
    icon: '🥚',
    color: 'from-yellow-400 to-orange-400',
    foods: {
      'Trứng gà luộc': { calo: 155, protein: 13, carb: 1.1, fat: 11 },
      'Trứng chiên ít dầu': { calo: 196, protein: 13.6, carb: 0.7, fat: 15 },
      'Lòng trắng trứng': { calo: 52, protein: 11, carb: 0.7, fat: 0.2 },
    }
  },
  '🍖 Thịt & Cá': {
    icon: '🍖',
    color: 'from-red-400 to-pink-400',
    foods: {
      'Ức gà kho gừng': { calo: 165, protein: 31, carb: 0, fat: 3.6 },
      'Ức gà hấp': { calo: 165, protein: 31, carb: 0, fat: 3.6 },
      '<PERSON><PERSON> chiên nước mắm (không da)': { calo: 190, protein: 29, carb: 2, fat: 7 },
      'Thịt heo nạc luộc': { calo: 143, protein: 26, carb: 0, fat: 3.5 },
      'Thịt heo nạc bằm': { calo: 143, protein: 26, carb: 0, fat: 3.5 },
      'Thịt bò xào': { calo: 250, protein: 26, carb: 0, fat: 15 },
      'Bò kho': { calo: 200, protein: 25, carb: 3, fat: 10 },
      'Cá basa áp chảo': { calo: 120, protein: 20, carb: 0, fat: 4 },
      'Cá basa nướng giấy bạc': { calo: 90, protein: 19, carb: 0, fat: 1.5 },
      'Cá điêu hồng chiên áp chảo': { calo: 128, protein: 20, carb: 0, fat: 4.5 },
      'Cá hộp (cá nục sốt cà)': { calo: 158, protein: 25, carb: 4, fat: 5 },
    }
  },
  '🍚 Tinh bột & Rau củ': {
    icon: '🍚',
    color: 'from-green-400 to-emerald-400',
    foods: {
      'Khoai lang luộc': { calo: 86, protein: 1.6, carb: 20, fat: 0.1 },
      'Cơm trắng (gạo tẻ)': { calo: 130, protein: 2.7, carb: 28, fat: 0.3 },
      'Bánh mì nguyên cám': { calo: 247, protein: 13, carb: 41, fat: 4.2 },
      'Rau muống luộc': { calo: 18, protein: 2.6, carb: 3.1, fat: 0.2 },
      'Cải thìa luộc': { calo: 13, protein: 1.5, carb: 2.2, fat: 0.2 },
      'Cải ngọt xào': { calo: 20, protein: 2, carb: 3.5, fat: 0.3 },
      'Cải xanh luộc': { calo: 25, protein: 3, carb: 4, fat: 0.4 },
      'Đậu que luộc': { calo: 35, protein: 1.8, carb: 7, fat: 0.1 },
      'Bông cải luộc': { calo: 25, protein: 3, carb: 5, fat: 0.3 },
      'Dưa leo': { calo: 16, protein: 0.7, carb: 4, fat: 0.1 },
      'Dưa leo trộn chua ngọt': { calo: 25, protein: 0.7, carb: 6, fat: 0.1 },
      'Cà chua': { calo: 18, protein: 0.9, carb: 3.9, fat: 0.2 },
    }
  },
  '🍎 Trái cây': {
    icon: '🍎',
    color: 'from-purple-400 to-pink-400',
    foods: {
      'Chuối': { calo: 89, protein: 1.1, carb: 23, fat: 0.3 },
      'Cam': { calo: 47, protein: 0.9, carb: 12, fat: 0.1 },
      'Ổi': { calo: 68, protein: 2.6, carb: 14, fat: 1 },
      'Táo': { calo: 52, protein: 0.3, carb: 14, fat: 0.2 },
      'Thanh long': { calo: 60, protein: 1.2, carb: 13, fat: 0.4 },
      'Dưa hấu': { calo: 30, protein: 0.6, carb: 8, fat: 0.2 },
      'Đu đủ': { calo: 43, protein: 0.5, carb: 11, fat: 0.3 },
    }
  },
  '🥛 Sữa & Đậu phụ': {
    icon: '🥛',
    color: 'from-blue-400 to-cyan-400',
    foods: {
      'Sữa tươi không đường': { calo: 42, protein: 3.4, carb: 5, fat: 1 },
      'Đậu hũ non': { calo: 76, protein: 8, carb: 1.9, fat: 4.8 },
      'Đậu phụ hấp': { calo: 76, protein: 8, carb: 1.9, fat: 4.8 },
    }
  },
  '🍲 Canh & Nước dùng': {
    icon: '🍲',
    color: 'from-teal-400 to-green-400',
    foods: {
      'Canh bí đỏ nấu thịt bằm': { calo: 40, protein: 3.5, carb: 3.5, fat: 1.5 },
      'Canh bí xanh': { calo: 25, protein: 2, carb: 4, fat: 0.5 },
      'Canh cải thìa': { calo: 20, protein: 2, carb: 2.5, fat: 0.5 },
      'Canh mồng tơi nấu tôm khô': { calo: 30, protein: 4, carb: 2, fat: 1 },
      'Canh mướp nấu tôm': { calo: 35, protein: 5, carb: 2.5, fat: 1 },
      'Canh bí nấu tôm': { calo: 35, protein: 5, carb: 3, fat: 1 },
      'Canh rau dền': { calo: 25, protein: 3, carb: 2, fat: 0.5 },
    }
  },
  '🍳 Món xào & Nấu': {
    icon: '🍳',
    color: 'from-orange-400 to-red-400',
    foods: {
      'Bí đỏ xào với thịt bằm': { calo: 60, protein: 4, carb: 6, fat: 2.5 },
      'Đậu hũ non sốt cà': { calo: 90, protein: 8, carb: 5, fat: 5 },
    }
  }
};

// Tạo foodData từ foodCategories để tương thích với code cũ
const foodData = {};
Object.values(foodCategories).forEach(category => {
  Object.assign(foodData, category.foods);
});

const App = () => {
  const [selectedFoods, setSelectedFoods] = useState([]);
  const [entries, setEntries] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('add'); // 'add' hoặc 'summary'

  // Tải dữ liệu từ localStorage khi component mount
  useEffect(() => {
    const savedEntries = localStorage.getItem('foodEntries');
    if (savedEntries) {
      setEntries(JSON.parse(savedEntries));
    }
  }, []);

  // Lưu dữ liệu vào localStorage khi entries thay đổi
  useEffect(() => {
    localStorage.setItem('foodEntries', JSON.stringify(entries));
  }, [entries]);

  // Thêm món ăn vào danh sách chọn
  const addToSelected = (foodName) => {
    if (!selectedFoods.find(item => item.name === foodName)) {
      setSelectedFoods([...selectedFoods, {
        name: foodName,
        grams: 100,
        id: Date.now()
      }]);
    }
  };

  // Xóa món ăn khỏi danh sách chọn
  const removeFromSelected = (id) => {
    setSelectedFoods(selectedFoods.filter(item => item.id !== id));
  };

  // Cập nhật số gram cho món ăn đã chọn
  const updateGrams = (id, newGrams) => {
    setSelectedFoods(selectedFoods.map(item =>
      item.id === id ? { ...item, grams: newGrams } : item
    ));
  };

  // Thêm tất cả món đã chọn vào entries
  const addSelectedToMeal = () => {
    if (selectedFoods.length > 0) {
      const newEntries = selectedFoods.map(item => ({
        ...item,
        id: Date.now() + Math.random(),
        addedAt: new Date().toLocaleString('vi-VN')
      }));
      setEntries([...entries, ...newEntries]);
      setSelectedFoods([]);
      setSearchTerm('');
    }
  };

  // Xóa món ăn khỏi entries
  const removeFood = (id) => {
    setEntries(entries.filter(entry => entry.id !== id));
  };

  // Xóa tất cả
  const clearAll = () => {
    setEntries([]);
  };

  // Xóa danh sách chọn
  const clearSelected = () => {
    setSelectedFoods([]);
  };

  const exportData = () => {
    const exportText = `
🥗 THÔNG TIN DINH DƯỠNG THỰC ĐƠN

📋 Danh sách món ăn:
${entries.map(item => `• ${item.name}: ${item.grams}g`).join('\n')}

📊 Tổng dinh dưỡng:
• Calories: ${totals.calo.toFixed(1)} kcal
• Protein: ${totals.protein.toFixed(1)} g (${((totals.protein / totals.calo) * 100).toFixed(1)}% calories)
• Carbohydrate: ${totals.carb.toFixed(1)} g (${((totals.carb * 4 / totals.calo) * 100).toFixed(1)}% calories)
• Fat: ${totals.fat.toFixed(1)} g (${((totals.fat * 9 / totals.calo) * 100).toFixed(1)}% calories)

💡 Khuyến nghị: Protein 15-25%, Carbs 45-65%, Fat 20-35%
    `.trim();

    navigator.clipboard.writeText(exportText).then(() => {
      alert('Đã sao chép thông tin dinh dưỡng vào clipboard!');
    }).catch(() => {
      // Fallback cho trình duyệt không hỗ trợ clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = exportText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Đã sao chép thông tin dinh dưỡng vào clipboard!');
    });
  };

  // Lọc thực phẩm theo từ khóa tìm kiếm và nhóm theo danh mục
  const getFilteredCategories = () => {
    if (!searchTerm.trim()) {
      return foodCategories;
    }

    const filtered = {};
    Object.entries(foodCategories).forEach(([categoryName, categoryData]) => {
      const filteredFoods = {};
      Object.entries(categoryData.foods).forEach(([foodName, foodInfo]) => {
        if (foodName.toLowerCase().includes(searchTerm.toLowerCase())) {
          filteredFoods[foodName] = foodInfo;
        }
      });

      if (Object.keys(filteredFoods).length > 0) {
        filtered[categoryName] = {
          ...categoryData,
          foods: filteredFoods
        };
      }
    });

    return filtered;
  };

  const filteredCategories = getFilteredCategories();

  const totals = entries.reduce(
    (acc, item) => {
      const data = foodData[item.name];
      const factor = item.grams / 100;
      acc.calo += data.calo * factor;
      acc.protein += data.protein * factor;
      acc.carb += data.carb * factor;
      acc.fat += data.fat * factor;
      return acc;
    },
    { calo: 0, protein: 0, carb: 0, fat: 0 }
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm shadow-xl border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full mb-4 shadow-lg">
              <span className="text-3xl">🍽️</span>
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-3">
              Quản Lý Dinh Dưỡng
            </h1>
            <p className="text-xl text-gray-600 mb-2">Thực Đơn Cá Nhân</p>
            <p className="text-gray-500 max-w-2xl mx-auto">
              Chọn nhiều món ăn Việt Nam yêu thích và tính toán dinh dưỡng tổng thể một cách dễ dàng
            </p>

            {/* Stats nhanh */}
            <div className="flex justify-center gap-6 mt-6">
              <div className="bg-white/60 backdrop-blur-sm rounded-xl px-4 py-2 shadow-md">
                <div className="text-sm text-gray-600">Tổng món ăn</div>
                <div className="text-2xl font-bold text-indigo-600">{Object.keys(foodData).length}</div>
              </div>
              <div className="bg-white/60 backdrop-blur-sm rounded-xl px-4 py-2 shadow-md">
                <div className="text-sm text-gray-600">Đã chọn</div>
                <div className="text-2xl font-bold text-purple-600">{entries.length}</div>
              </div>
              <div className="bg-white/60 backdrop-blur-sm rounded-xl px-4 py-2 shadow-md">
                <div className="text-sm text-gray-600">Calories</div>
                <div className="text-2xl font-bold text-orange-600">{totals.calo.toFixed(0)}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Tab Navigation */}
        <div className="flex justify-center mb-10">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-xl border border-white/20">
            <button
              onClick={() => setActiveTab('add')}
              className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 flex items-center gap-3 ${
                activeTab === 'add'
                  ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg transform scale-105'
                  : 'text-gray-600 hover:text-indigo-600 hover:bg-white/50'
              }`}
            >
              <span className="text-xl">🛒</span>
              <div className="text-left">
                <div>Chọn Món Ăn</div>
                <div className="text-xs opacity-75">Tìm kiếm & thêm món</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('summary')}
              className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 flex items-center gap-3 ${
                activeTab === 'summary'
                  ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg transform scale-105'
                  : 'text-gray-600 hover:text-indigo-600 hover:bg-white/50'
              }`}
            >
              <span className="text-xl">📊</span>
              <div className="text-left">
                <div>Tổng Kết</div>
                <div className="text-xs opacity-75">{entries.length} món đã chọn</div>
              </div>
              {entries.length > 0 && (
                <span className="bg-white/20 text-xs px-2 py-1 rounded-full">
                  {entries.length}
                </span>
              )}
            </button>
          </div>
        </div>

        {activeTab === 'add' && (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Phần tìm kiếm và chọn món */}
            <div className="lg:col-span-2">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 border border-white/20">
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <span className="text-xl">🔍</span>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800">Tìm Kiếm Món Ăn</h2>
                    <p className="text-gray-600">Khám phá {Object.keys(foodData).length} món ăn Việt Nam</p>
                  </div>
                </div>

                {/* Tìm kiếm */}
                <div className="mb-8">
                  <div className="relative">
                    <input
                      type="text"
                      className="w-full border-2 border-gray-200 rounded-2xl pl-14 pr-6 py-4 text-lg focus:ring-4 focus:ring-indigo-200 focus:border-indigo-400 transition-all duration-200 bg-white/70 backdrop-blur-sm"
                      placeholder="Nhập tên món ăn để tìm kiếm..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <span className="text-xl">🔍</span>
                    </div>
                    {searchTerm && (
                      <button
                        onClick={() => setSearchTerm('')}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        <span className="text-xl">❌</span>
                      </button>
                    )}
                  </div>
                  {searchTerm && (
                    <p className="text-sm text-gray-500 mt-2">
                      Tìm thấy {Object.keys(filteredCategories).reduce((total, cat) =>
                        total + Object.keys(filteredCategories[cat].foods).length, 0
                      )} món ăn
                    </p>
                  )}
                </div>

                {/* Danh sách món ăn theo danh mục */}
                <div className="max-h-[600px] overflow-y-auto space-y-6">
                  {Object.entries(filteredCategories).map(([categoryName, categoryData]) => (
                    <div key={categoryName} className="bg-gradient-to-r from-gray-50 to-white rounded-xl p-6 border border-gray-100">
                      {/* Header danh mục */}
                      <div className={`bg-gradient-to-r ${categoryData.color} rounded-lg p-4 mb-4`}>
                        <h3 className="text-xl font-bold text-white flex items-center gap-3">
                          <span className="text-2xl">{categoryData.icon}</span>
                          {categoryName}
                          <span className="bg-white/20 px-2 py-1 rounded-full text-sm">
                            {Object.keys(categoryData.foods).length} món
                          </span>
                        </h3>
                      </div>

                      {/* Danh sách món ăn trong danh mục */}
                      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {Object.entries(categoryData.foods).map(([foodName, foodInfo]) => (
                          <div
                            key={foodName}
                            className="bg-white border-2 border-gray-100 rounded-lg p-4 hover:border-indigo-300 hover:shadow-lg transition-all duration-200 cursor-pointer group"
                            onClick={() => addToSelected(foodName)}
                          >
                            <div className="flex items-start justify-between mb-3">
                              <h4 className="font-medium text-gray-800 text-sm leading-tight group-hover:text-indigo-600 transition-colors">
                                {foodName}
                              </h4>
                              <span className="text-lg">{categoryData.icon}</span>
                            </div>

                            <div className="space-y-1 text-xs text-gray-600">
                              <div className="flex justify-between">
                                <span>Calories:</span>
                                <span className="font-medium text-orange-600">{foodInfo.calo} kcal</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Protein:</span>
                                <span className="font-medium text-red-600">{foodInfo.protein}g</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Carbs:</span>
                                <span className="font-medium text-blue-600">{foodInfo.carb}g</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Fat:</span>
                                <span className="font-medium text-yellow-600">{foodInfo.fat}g</span>
                              </div>
                            </div>

                            <button className="w-full mt-3 bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white text-xs py-2 px-3 rounded-lg transition-all duration-200 transform group-hover:scale-105">
                              ➕ Thêm vào giỏ
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}

                  {Object.keys(filteredCategories).length === 0 && (
                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">🔍</div>
                      <p className="text-gray-500 text-lg">Không tìm thấy món ăn nào</p>
                      <p className="text-gray-400 text-sm mt-2">Thử tìm kiếm với từ khóa khác</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Phần danh sách món đã chọn */}
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-white/20 sticky top-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
                      <span className="text-lg">🛍️</span>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-800">Món Đã Chọn</h2>
                      <p className="text-sm text-gray-600">{selectedFoods.length} món trong giỏ</p>
                    </div>
                  </div>
                  {selectedFoods.length > 0 && (
                    <button
                      onClick={clearSelected}
                      className="bg-red-100 hover:bg-red-200 text-red-600 px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                    >
                      <span>🗑️</span>
                      Xóa tất cả
                    </button>
                  )}
                </div>

                <div className="space-y-4 max-h-80 overflow-y-auto">
                  {selectedFoods.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-5xl mb-4">🛒</div>
                      <p className="text-gray-500 text-lg">Giỏ hàng trống</p>
                      <p className="text-gray-400 text-sm mt-2">Chọn món ăn từ danh sách bên trái</p>
                    </div>
                  ) : (
                    selectedFoods.map((item) => (
                      <div key={item.id} className="bg-gradient-to-r from-white to-gray-50 rounded-xl p-4 border border-gray-100 hover:shadow-md transition-all">
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="font-semibold text-gray-800 text-sm leading-tight">{item.name}</h3>
                          <button
                            onClick={() => removeFromSelected(item.id)}
                            className="text-red-400 hover:text-red-600 p-1 hover:bg-red-50 rounded-lg transition-colors"
                          >
                            <span className="text-sm">❌</span>
                          </button>
                        </div>

                        <div className="flex items-center gap-3 mb-3">
                          <div className="flex items-center gap-2">
                            <input
                              type="number"
                              value={item.grams}
                              onChange={(e) => updateGrams(item.id, Number(e.target.value))}
                              className="w-20 border-2 border-gray-200 rounded-lg px-3 py-2 text-sm font-medium focus:ring-2 focus:ring-indigo-200 focus:border-indigo-400 transition-all"
                              min="1"
                            />
                            <span className="text-sm text-gray-600 font-medium">gram</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="bg-orange-50 px-2 py-1 rounded-lg">
                            <span className="text-orange-600 font-medium">
                              🔥 {(foodData[item.name].calo * item.grams / 100).toFixed(1)} kcal
                            </span>
                          </div>
                          <div className="bg-red-50 px-2 py-1 rounded-lg">
                            <span className="text-red-600 font-medium">
                              🥩 {(foodData[item.name].protein * item.grams / 100).toFixed(1)}g
                            </span>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {selectedFoods.length > 0 && (
                  <div className="mt-6 space-y-3">
                    {/* Tổng kết nhanh */}
                    <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4">
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div className="text-center">
                          <div className="text-lg font-bold text-orange-600">
                            {selectedFoods.reduce((total, item) =>
                              total + (foodData[item.name].calo * item.grams / 100), 0
                            ).toFixed(0)}
                          </div>
                          <div className="text-xs text-gray-600">Calories</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-red-600">
                            {selectedFoods.reduce((total, item) =>
                              total + (foodData[item.name].protein * item.grams / 100), 0
                            ).toFixed(1)}g
                          </div>
                          <div className="text-xs text-gray-600">Protein</div>
                        </div>
                      </div>
                    </div>

                    <button
                      onClick={addSelectedToMeal}
                      className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center gap-3"
                    >
                      <span className="text-xl">✅</span>
                      <div className="text-left">
                        <div>Thêm Vào Thực Đơn</div>
                        <div className="text-xs opacity-90">{selectedFoods.length} món đã chọn</div>
                      </div>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'summary' && (
          <div className="space-y-8">
            {/* Tổng quan dinh dưỡng */}
            {entries.length > 0 && (
              <div className="bg-white rounded-xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-center text-gray-800 mb-6">
                  📊 Tổng Kết Dinh Dưỡng
                </h2>
                <div className="grid md:grid-cols-4 gap-6 mb-8">
                  <div className="bg-gradient-to-br from-orange-100 to-orange-200 p-6 rounded-xl text-center">
                    <div className="text-3xl font-bold text-orange-600 mb-2">
                      {totals.calo.toFixed(1)}
                    </div>
                    <div className="text-sm text-orange-700 font-medium">Calories (kcal)</div>
                  </div>
                  <div className="bg-gradient-to-br from-red-100 to-red-200 p-6 rounded-xl text-center">
                    <div className="text-3xl font-bold text-red-600 mb-2">
                      {totals.protein.toFixed(1)}
                    </div>
                    <div className="text-sm text-red-700 font-medium">Protein (g)</div>
                  </div>
                  <div className="bg-gradient-to-br from-blue-100 to-blue-200 p-6 rounded-xl text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {totals.carb.toFixed(1)}
                    </div>
                    <div className="text-sm text-blue-700 font-medium">Carbs (g)</div>
                  </div>
                  <div className="bg-gradient-to-br from-yellow-100 to-yellow-200 p-6 rounded-xl text-center">
                    <div className="text-3xl font-bold text-yellow-600 mb-2">
                      {totals.fat.toFixed(1)}
                    </div>
                    <div className="text-sm text-yellow-700 font-medium">Fat (g)</div>
                  </div>
                </div>

                {/* Phân tích tỷ lệ dinh dưỡng */}
                <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-xl">
                  <h3 className="font-bold text-gray-800 mb-4 text-center">💡 Phân Tích Tỷ Lệ Dinh Dưỡng</h3>
                  <div className="grid md:grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-600">
                        {((totals.protein * 4 / totals.calo) * 100).toFixed(1)}%
                      </div>
                      <div className="text-gray-600">Protein</div>
                      <div className="text-xs text-gray-500">(Khuyến nghị: 15-25%)</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-600">
                        {((totals.carb * 4 / totals.calo) * 100).toFixed(1)}%
                      </div>
                      <div className="text-gray-600">Carbohydrate</div>
                      <div className="text-xs text-gray-500">(Khuyến nghị: 45-65%)</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-yellow-600">
                        {((totals.fat * 9 / totals.calo) * 100).toFixed(1)}%
                      </div>
                      <div className="text-gray-600">Fat</div>
                      <div className="text-xs text-gray-500">(Khuyến nghị: 20-35%)</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Danh sách chi tiết món ăn */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800">
                  🍽️ Chi Tiết Thực Đơn ({entries.length} món)
                </h2>
                {entries.length > 0 && (
                  <div className="flex gap-3">
                    <button
                      onClick={exportData}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      📋 Sao Chép
                    </button>
                    <button
                      onClick={clearAll}
                      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      🗑️ Xóa Tất Cả
                    </button>
                  </div>
                )}
              </div>

              <div className="space-y-3 max-h-96 overflow-y-auto">
                {entries.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🍽️</div>
                    <p className="text-gray-500 text-lg">Chưa có món ăn nào trong thực đơn</p>
                    <p className="text-gray-400 text-sm mt-2">Hãy chuyển sang tab "Chọn Món Ăn" để thêm món</p>
                  </div>
                ) : (
                  entries.map((item) => (
                    <div key={item.id} className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-800 mb-1">{item.name}</h3>
                          <div className="text-sm text-gray-600 mb-2">{item.grams}g</div>
                          {item.addedAt && (
                            <div className="text-xs text-gray-400">Thêm lúc: {item.addedAt}</div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-2">
                            <div>🔥 {(foodData[item.name].calo * item.grams / 100).toFixed(1)} kcal</div>
                            <div>🥩 {(foodData[item.name].protein * item.grams / 100).toFixed(1)}g</div>
                            <div>🍞 {(foodData[item.name].carb * item.grams / 100).toFixed(1)}g</div>
                            <div>🧈 {(foodData[item.name].fat * item.grams / 100).toFixed(1)}g</div>
                          </div>
                          <button
                            onClick={() => removeFood(item.id)}
                            className="text-red-500 hover:text-red-700 p-1"
                          >
                            ❌
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-white/80 backdrop-blur-sm border-t border-white/20 mt-16">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="text-center">
            <div className="flex justify-center items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-lg">🍽️</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800">Quản Lý Dinh Dưỡng</h3>
            </div>
            <p className="text-gray-600 mb-2">Ứng dụng quản lý dinh dưỡng thực đơn cá nhân</p>
            <p className="text-sm text-gray-500">
              Dữ liệu được lưu tự động trên trình duyệt của bạn •
              <span className="mx-2">•</span>
              {Object.keys(foodData).length} món ăn Việt Nam
            </p>

            <div className="flex justify-center gap-6 mt-6 text-xs text-gray-400">
              <div className="flex items-center gap-1">
                <span>💾</span>
                <span>Lưu trữ cục bộ</span>
              </div>
              <div className="flex items-center gap-1">
                <span>🔒</span>
                <span>Bảo mật dữ liệu</span>
              </div>
              <div className="flex items-center gap-1">
                <span>📱</span>
                <span>Responsive design</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
