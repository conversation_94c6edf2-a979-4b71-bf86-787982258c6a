import { useState, useEffect } from 'react';
import './App.css';

// D<PERSON> liệu dinh dưỡng được phân loại theo nhóm thực phẩm
const foodCategories = {
  '🥚 Trứng & Sản phẩm từ trứng': {
    icon: '🥚',
    foods: {
      'Trứng gà luộc': { calo: 155, protein: 13, carb: 1.1, fat: 11 },
      'Trứng chiên ít dầu': { calo: 196, protein: 13.6, carb: 0.7, fat: 15 },
      'Lòng trắng trứng': { calo: 52, protein: 11, carb: 0.7, fat: 0.2 },
    }
  },
  '🍖 Thịt & Cá': {
    icon: '🍖',
    foods: {
      'Ức gà kho gừng': { calo: 165, protein: 31, carb: 0, fat: 3.6 },
      'Ức gà hấp': { calo: 165, protein: 31, carb: 0, fat: 3.6 },
      '<PERSON><PERSON> chiên nước mắm (không da)': { calo: 190, protein: 29, carb: 2, fat: 7 },
      'Thịt heo nạc luộc': { calo: 143, protein: 26, carb: 0, fat: 3.5 },
      'Thịt heo nạc bằm': { calo: 143, protein: 26, carb: 0, fat: 3.5 },
      'Thịt bò xào': { calo: 250, protein: 26, carb: 0, fat: 15 },
      'Bò kho': { calo: 200, protein: 25, carb: 3, fat: 10 },
      'Cá basa áp chảo': { calo: 120, protein: 20, carb: 0, fat: 4 },
      'Cá basa nướng giấy bạc': { calo: 90, protein: 19, carb: 0, fat: 1.5 },
      'Cá điêu hồng chiên áp chảo': { calo: 128, protein: 20, carb: 0, fat: 4.5 },
      'Cá hộp (cá nục sốt cà)': { calo: 158, protein: 25, carb: 4, fat: 5 },
    }
  },
  '🍚 Tinh bột & Rau củ': {
    icon: '🍚',
    foods: {
      'Khoai lang luộc': { calo: 86, protein: 1.6, carb: 20, fat: 0.1 },
      'Cơm trắng (gạo tẻ)': { calo: 130, protein: 2.7, carb: 28, fat: 0.3 },
      'Bánh mì nguyên cám': { calo: 247, protein: 13, carb: 41, fat: 4.2 },
      'Rau muống luộc': { calo: 18, protein: 2.6, carb: 3.1, fat: 0.2 },
      'Cải thìa luộc': { calo: 13, protein: 1.5, carb: 2.2, fat: 0.2 },
      'Cải ngọt xào': { calo: 20, protein: 2, carb: 3.5, fat: 0.3 },
      'Cải xanh luộc': { calo: 25, protein: 3, carb: 4, fat: 0.4 },
      'Đậu que luộc': { calo: 35, protein: 1.8, carb: 7, fat: 0.1 },
      'Bông cải luộc': { calo: 25, protein: 3, carb: 5, fat: 0.3 },
      'Dưa leo': { calo: 16, protein: 0.7, carb: 4, fat: 0.1 },
      'Dưa leo trộn chua ngọt': { calo: 25, protein: 0.7, carb: 6, fat: 0.1 },
      'Cà chua': { calo: 18, protein: 0.9, carb: 3.9, fat: 0.2 },
    }
  },
  '🍎 Trái cây': {
    icon: '🍎',
    foods: {
      'Chuối': { calo: 89, protein: 1.1, carb: 23, fat: 0.3 },
      'Cam': { calo: 47, protein: 0.9, carb: 12, fat: 0.1 },
      'Ổi': { calo: 68, protein: 2.6, carb: 14, fat: 1 },
      'Táo': { calo: 52, protein: 0.3, carb: 14, fat: 0.2 },
      'Thanh long': { calo: 60, protein: 1.2, carb: 13, fat: 0.4 },
      'Dưa hấu': { calo: 30, protein: 0.6, carb: 8, fat: 0.2 },
      'Đu đủ': { calo: 43, protein: 0.5, carb: 11, fat: 0.3 },
    }
  },
  '🥛 Sữa & Đậu phụ': {
    icon: '🥛',
    foods: {
      'Sữa tươi không đường': { calo: 42, protein: 3.4, carb: 5, fat: 1 },
      'Đậu hũ non': { calo: 76, protein: 8, carb: 1.9, fat: 4.8 },
      'Đậu phụ hấp': { calo: 76, protein: 8, carb: 1.9, fat: 4.8 },
    }
  },
  '🍲 Canh & Nước dùng': {
    icon: '🍲',
    foods: {
      'Canh bí đỏ nấu thịt bằm': { calo: 40, protein: 3.5, carb: 3.5, fat: 1.5 },
      'Canh bí xanh': { calo: 25, protein: 2, carb: 4, fat: 0.5 },
      'Canh cải thìa': { calo: 20, protein: 2, carb: 2.5, fat: 0.5 },
      'Canh mồng tơi nấu tôm khô': { calo: 30, protein: 4, carb: 2, fat: 1 },
      'Canh mướp nấu tôm': { calo: 35, protein: 5, carb: 2.5, fat: 1 },
      'Canh bí nấu tôm': { calo: 35, protein: 5, carb: 3, fat: 1 },
      'Canh rau dền': { calo: 25, protein: 3, carb: 2, fat: 0.5 },
    }
  },
  '🍳 Món xào & Nấu': {
    icon: '🍳',
    foods: {
      'Bí đỏ xào với thịt bằm': { calo: 60, protein: 4, carb: 6, fat: 2.5 },
      'Đậu hũ non sốt cà': { calo: 90, protein: 8, carb: 5, fat: 5 },
    }
  }
};

// Tạo foodData từ foodCategories
const foodData = {};
Object.values(foodCategories).forEach(category => {
  Object.assign(foodData, category.foods);
});

const App = () => {
  const [selectedFoods, setSelectedFoods] = useState([]);
  const [entries, setEntries] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('add');

  useEffect(() => {
    const savedEntries = localStorage.getItem('foodEntries');
    if (savedEntries) {
      setEntries(JSON.parse(savedEntries));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('foodEntries', JSON.stringify(entries));
  }, [entries]);

  const addToSelected = (foodName) => {
    if (!selectedFoods.find(item => item.name === foodName)) {
      setSelectedFoods([...selectedFoods, {
        name: foodName,
        grams: 100,
        id: Date.now()
      }]);
    }
  };

  const removeFromSelected = (id) => {
    setSelectedFoods(selectedFoods.filter(item => item.id !== id));
  };

  const updateGrams = (id, newGrams) => {
    setSelectedFoods(selectedFoods.map(item =>
      item.id === id ? { ...item, grams: newGrams } : item
    ));
  };

  const addSelectedToMeal = () => {
    if (selectedFoods.length > 0) {
      const newEntries = selectedFoods.map(item => ({
        ...item,
        id: Date.now() + Math.random(),
        addedAt: new Date().toLocaleString('vi-VN')
      }));
      setEntries([...entries, ...newEntries]);
      setSelectedFoods([]);
      setSearchTerm('');
    }
  };

  const removeFood = (id) => {
    setEntries(entries.filter(entry => entry.id !== id));
  };

  const clearAll = () => {
    setEntries([]);
  };

  const clearSelected = () => {
    setSelectedFoods([]);
  };

  const getFilteredCategories = () => {
    if (!searchTerm.trim()) {
      return foodCategories;
    }
    
    const filtered = {};
    Object.entries(foodCategories).forEach(([categoryName, categoryData]) => {
      const filteredFoods = {};
      Object.entries(categoryData.foods).forEach(([foodName, foodInfo]) => {
        if (foodName.toLowerCase().includes(searchTerm.toLowerCase())) {
          filteredFoods[foodName] = foodInfo;
        }
      });
      
      if (Object.keys(filteredFoods).length > 0) {
        filtered[categoryName] = {
          ...categoryData,
          foods: filteredFoods
        };
      }
    });
    
    return filtered;
  };

  const filteredCategories = getFilteredCategories();

  const totals = entries.reduce(
    (acc, item) => {
      const data = foodData[item.name];
      const factor = item.grams / 100;
      acc.calo += data.calo * factor;
      acc.protein += data.protein * factor;
      acc.carb += data.carb * factor;
      acc.fat += data.fat * factor;
      return acc;
    },
    { calo: 0, protein: 0, carb: 0, fat: 0 }
  );

  const exportData = () => {
    const exportText = `
🥗 THÔNG TIN DINH DƯỠNG THỰC ĐƠN

📋 Danh sách món ăn:
${entries.map(item => `• ${item.name}: ${item.grams}g`).join('\n')}

📊 Tổng dinh dưỡng:
• Calories: ${totals.calo.toFixed(1)} kcal
• Protein: ${totals.protein.toFixed(1)} g
• Carbohydrate: ${totals.carb.toFixed(1)} g
• Fat: ${totals.fat.toFixed(1)} g
    `.trim();

    navigator.clipboard.writeText(exportText).then(() => {
      alert('Đã sao chép thông tin dinh dưỡng vào clipboard!');
    }).catch(() => {
      const textArea = document.createElement('textarea');
      textArea.value = exportText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Đã sao chép thông tin dinh dưỡng vào clipboard!');
    });
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <h1>🍽️ Quản Lý Dinh Dưỡng Thực Đơn</h1>
          <p>Chọn món ăn và tính toán dinh dưỡng một cách dễ dàng</p>
          
          <div className="stats">
            <div className="stat-item">
              <span className="stat-number">{Object.keys(foodData).length}</span>
              <span className="stat-label">Tổng món</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{entries.length}</span>
              <span className="stat-label">Đã chọn</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{totals.calo.toFixed(0)}</span>
              <span className="stat-label">Calories</span>
            </div>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="tab-nav">
          <div className="tab-buttons">
            <button
              onClick={() => setActiveTab('add')}
              className={`tab-button ${activeTab === 'add' ? 'active' : ''}`}
            >
              🛒 Chọn Món Ăn
            </button>
            <button
              onClick={() => setActiveTab('summary')}
              className={`tab-button ${activeTab === 'summary' ? 'active' : ''}`}
            >
              📊 Tổng Kết ({entries.length})
            </button>
          </div>
        </div>

        {activeTab === 'add' && (
          <div className="content-grid">
            <div className="card">
              <div className="card-header">
                <h2 className="card-title">🔍 Tìm Kiếm Món Ăn</h2>
              </div>

              <div className="search-container">
                <input
                  type="text"
                  className="search-input"
                  placeholder="Nhập tên món ăn để tìm kiếm..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm && (
                  <p className="search-results">
                    Tìm thấy {Object.keys(filteredCategories).reduce((total, cat) =>
                      total + Object.keys(filteredCategories[cat].foods).length, 0
                    )} món ăn
                  </p>
                )}
              </div>

              <div className="categories-container">
                {Object.entries(filteredCategories).map(([categoryName, categoryData]) => (
                  <div key={categoryName} className="category">
                    <div className="category-header">
                      <span>{categoryData.icon}</span>
                      {categoryName.replace(/^🥚|🍖|🍚|🍎|🥛|🍲|🍳\s/, '')}
                      <span className="category-count">
                        {Object.keys(categoryData.foods).length} món
                      </span>
                    </div>

                    <div className="food-list">
                      {Object.entries(categoryData.foods).map(([foodName, foodInfo]) => (
                        <div key={foodName} className="food-item">
                          <div className="food-info">
                            <h4>{foodName}</h4>
                            <div className="food-nutrition">
                              {foodInfo.calo} kcal • {foodInfo.protein}g protein • {foodInfo.carb}g carbs • {foodInfo.fat}g fat
                            </div>
                          </div>

                          <button
                            onClick={() => addToSelected(foodName)}
                            className="add-button"
                          >
                            + Thêm
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}

                {Object.keys(filteredCategories).length === 0 && (
                  <div className="empty-state">
                    <div className="empty-state-icon">🔍</div>
                    <p>Không tìm thấy món ăn nào</p>
                  </div>
                )}
              </div>
            </div>

            <div className="card selected-foods">
              <div className="selected-header">
                <h2 className="card-title">🛍️ Món Đã Chọn ({selectedFoods.length})</h2>
                {selectedFoods.length > 0 && (
                  <button onClick={clearSelected} className="clear-button">
                    Xóa tất cả
                  </button>
                )}
              </div>

              <div className="selected-list">
                {selectedFoods.length === 0 ? (
                  <div className="empty-state">
                    <div className="empty-state-icon">🛒</div>
                    <p>Chưa có món ăn nào được chọn</p>
                  </div>
                ) : (
                  selectedFoods.map((item) => (
                    <div key={item.id} className="selected-item">
                      <div className="selected-item-header">
                        <h4>{item.name}</h4>
                        <button
                          onClick={() => removeFromSelected(item.id)}
                          className="remove-button"
                        >
                          ✕
                        </button>
                      </div>

                      <div className="gram-input-container">
                        <input
                          type="number"
                          value={item.grams}
                          onChange={(e) => updateGrams(item.id, Number(e.target.value))}
                          className="gram-input"
                          min="1"
                        />
                        <span>gram</span>
                      </div>

                      <div className="nutrition-info">
                        {(foodData[item.name].calo * item.grams / 100).toFixed(1)} kcal •
                        {(foodData[item.name].protein * item.grams / 100).toFixed(1)}g protein
                      </div>
                    </div>
                  ))
                )}
              </div>

              {selectedFoods.length > 0 && (
                <button
                  onClick={addSelectedToMeal}
                  className="add-to-meal-button"
                >
                  ✅ Thêm Vào Thực Đơn ({selectedFoods.length} món)
                </button>
              )}
            </div>
          </div>
        )}

        {activeTab === 'summary' && (
          <div>
            {entries.length > 0 && (
              <div className="card">
                <h2 className="card-title" style={{textAlign: 'center', marginBottom: '2rem'}}>
                  📊 Tổng Kết Dinh Dưỡng
                </h2>
                <div className="summary-stats">
                  <div className="stat-card calories">
                    <div className="stat-value">{totals.calo.toFixed(1)}</div>
                    <div className="stat-card-label">Calories (kcal)</div>
                  </div>
                  <div className="stat-card protein">
                    <div className="stat-value">{totals.protein.toFixed(1)}</div>
                    <div className="stat-card-label">Protein (g)</div>
                  </div>
                  <div className="stat-card carbs">
                    <div className="stat-value">{totals.carb.toFixed(1)}</div>
                    <div className="stat-card-label">Carbs (g)</div>
                  </div>
                  <div className="stat-card fat">
                    <div className="stat-value">{totals.fat.toFixed(1)}</div>
                    <div className="stat-card-label">Fat (g)</div>
                  </div>
                </div>
              </div>
            )}

            <div className="card" style={{marginTop: '2rem'}}>
              <div className="card-header">
                <h2 className="card-title">🍽️ Chi Tiết Thực Đơn ({entries.length} món)</h2>
                {entries.length > 0 && (
                  <div style={{display: 'flex', gap: '0.75rem'}}>
                    <button onClick={exportData} style={{background: '#3b82f6', color: 'white', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer'}}>
                      📋 Sao Chép
                    </button>
                    <button onClick={clearAll} className="clear-button">
                      🗑️ Xóa Tất Cả
                    </button>
                  </div>
                )}
              </div>

              <div style={{maxHeight: '400px', overflowY: 'auto'}}>
                {entries.length === 0 ? (
                  <div className="empty-state">
                    <div className="empty-state-icon">🍽️</div>
                    <p>Chưa có món ăn nào trong thực đơn</p>
                    <p style={{fontSize: '0.9rem', color: '#94a3b8', marginTop: '0.5rem'}}>Hãy chuyển sang tab "Chọn Món Ăn" để thêm món</p>
                  </div>
                ) : (
                  entries.map((item) => (
                    <div key={item.id} className="selected-item" style={{marginBottom: '0.75rem'}}>
                      <div className="selected-item-header">
                        <div>
                          <h4>{item.name}</h4>
                          <div style={{fontSize: '0.9rem', color: '#64748b', marginTop: '0.25rem'}}>{item.grams}g</div>
                          {item.addedAt && (
                            <div style={{fontSize: '0.75rem', color: '#94a3b8'}}>Thêm lúc: {item.addedAt}</div>
                          )}
                        </div>
                        <div style={{textAlign: 'right'}}>
                          <div className="nutrition-info" style={{marginBottom: '0.5rem'}}>
                            🔥 {(foodData[item.name].calo * item.grams / 100).toFixed(1)} kcal<br/>
                            🥩 {(foodData[item.name].protein * item.grams / 100).toFixed(1)}g protein<br/>
                            🍞 {(foodData[item.name].carb * item.grams / 100).toFixed(1)}g carbs<br/>
                            🧈 {(foodData[item.name].fat * item.grams / 100).toFixed(1)}g fat
                          </div>
                          <button onClick={() => removeFood(item.id)} className="remove-button">
                            ❌
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}
      </main>

      <footer className="footer">
        <div className="footer-content">
          <p>📱 Ứng dụng quản lý dinh dưỡng thực đơn cá nhân</p>
          <p style={{marginTop: '0.5rem'}}>Dữ liệu được lưu tự động trên trình duyệt của bạn</p>
        </div>
      </footer>
    </div>
  );
};

export default App;
